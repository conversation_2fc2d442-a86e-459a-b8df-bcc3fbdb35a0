/**
 * @file circular_buffer.h
 * @brief Circular buffer for ESP32 real-time signal processing
 *
 * TODO: Implement memory-efficient circular buffer for ESP32
 *
 * This module should provide efficient circular buffer implementations for
 * storing and processing continuous signal data streams with ESP32 constraints:
 *
 * Key ESP32 Implementation Requirements:
 * 1. Memory Efficiency:
 *    - Use static allocation where possible to avoid heap fragmentation
 *    - Implement template specializations for common data types (float,
 * int16_t)
 *    - Consider fixed-point arithmetic for better performance
 *    - Target <10KB memory usage for typical buffer sizes
 *
 * 2. Thread Safety for FreeRTOS:
 *    - Use FreeRTOS mutexes/semaphores instead of std::mutex
 *    - Implement lock-free algorithms where possible for ISR compatibility
 *    - Consider using FreeRTOS queues for producer-consumer patterns
 *    - Handle priority inversion scenarios
 *
 * 3. Real-time Performance:
 *    - Optimize for constant-time operations (O(1) push/pop)
 *    - Minimize memory allocations during runtime
 *    - Use DMA-compatible memory alignment where needed
 *    - Consider cache-friendly data layouts
 *
 * 4. Power Optimization:
 *    - Implement efficient data structures that minimize CPU cycles
 *    - Support for low-power modes (buffer state preservation)
 *    - Minimize memory access patterns
 *
 * 5. Multi-channel Support:
 *    - Synchronized buffers for PPG + 3-axis accelerometer data
 *    - Efficient storage for different sampling rates
 *    - Support for timestamp synchronization
 *
 * 6. ESP32-specific Features:
 *    - Integration with ESP32 hardware timers
 *    - Support for dual-core processing scenarios
 *    - DMA buffer compatibility for high-speed data acquisition
 */

#ifndef BSPML_CIRCULAR_BUFFER_H
#define BSPML_CIRCULAR_BUFFER_H

// TODO: Implement circular buffer classes optimized for ESP32
// Include FreeRTOS integration, memory-efficient data structures,
// and real-time performance optimizations

/**
 * @brief Multi-channel circular buffer for synchronized signals
 *
 * This class manages multiple synchronized circular buffers,
 * useful for PPG + accelerometer data.
 */
template <typename T>
class MultiChannelBuffer {
 public:
  /**
   * @brief Constructor
   * @param num_channels Number of channels
   * @param capacity Buffer capacity per channel
   */
  MultiChannelBuffer(size_t num_channels, size_t capacity);

  /**
   * @brief Add synchronized samples to all channels
   * @param samples Vector of samples (one per channel)
   * @return True if successful
   */
  bool push(const std::vector<T>& samples);

  /**
   * @brief Add sample to specific channel
   * @param channel Channel index
   * @param sample Sample value
   * @return True if successful
   */
  bool push(size_t channel, const T& sample);

  /**
   * @brief Get recent samples from all channels
   * @param count Number of samples per channel
   * @return Vector of vectors (channels x samples)
   */
  std::vector<std::vector<T>> getRecent(size_t count) const;

  /**
   * @brief Get recent samples from specific channel
   * @param channel Channel index
   * @param count Number of samples
   * @return Vector of samples
   */
  std::vector<T> getRecent(size_t channel, size_t count) const;

  /**
   * @brief Get all samples from all channels
   * @return Vector of vectors (channels x samples)
   */
  std::vector<std::vector<T>> getAll() const;

  /**
   * @brief Get number of channels
   * @return Number of channels
   */
  size_t getNumChannels() const;

  /**
   * @brief Get buffer capacity
   * @return Buffer capacity per channel
   */
  size_t capacity() const;

  /**
   * @brief Get current size (minimum across all channels)
   * @return Number of synchronized samples available
   */
  size_t size() const;

  /**
   * @brief Clear all channels
   */
  void clear();

  /**
   * @brief Check if buffers are synchronized
   * @return True if all channels have same number of samples
   */
  bool isSynchronized() const;

 private:
  std::vector<std::unique_ptr<CircularBuffer<T>>> channels_;
  size_t num_channels_;
  mutable std::mutex sync_mutex_;
};

/**
 * @brief Specialized buffer for PPG and accelerometer data
 */
class PPGAccBuffer {
 public:
  /**
   * @brief Constructor
   * @param capacity Buffer capacity
   */
  explicit PPGAccBuffer(size_t capacity);

  /**
   * @brief Add synchronized PPG and accelerometer sample
   * @param ppg_sample PPG sample
   * @param acc_x Accelerometer X sample
   * @param acc_y Accelerometer Y sample
   * @param acc_z Accelerometer Z sample
   * @return True if successful
   */
  bool push(double ppg_sample, double acc_x, double acc_y, double acc_z);

  /**
   * @brief Add PPG sample only
   * @param ppg_sample PPG sample
   * @return True if successful
   */
  bool pushPPG(double ppg_sample);

  /**
   * @brief Get recent PPG samples
   * @param count Number of samples
   * @return Vector of PPG samples
   */
  std::vector<double> getRecentPPG(size_t count) const;

  /**
   * @brief Get recent accelerometer samples
   * @param count Number of samples
   * @return Vector of vectors (3 x count) for x, y, z axes
   */
  std::vector<std::vector<double>> getRecentAcc(size_t count) const;

  /**
   * @brief Get synchronized PPG and accelerometer data
   * @param count Number of samples
   * @return Pair of (PPG samples, ACC samples)
   */
  std::pair<std::vector<double>, std::vector<std::vector<double>>>
  getRecentSynchronized(size_t count) const;

  /**
   * @brief Check if accelerometer data is available
   * @return True if ACC data is being stored
   */
  bool hasAccelerometerData() const;

  /**
   * @brief Get buffer capacity
   * @return Buffer capacity
   */
  size_t capacity() const;

  /**
   * @brief Get current size
   * @return Number of samples
   */
  size_t size() const;

  /**
   * @brief Clear all data
   */
  void clear();

#endif  // BSPML_CIRCULAR_BUFFER_H
