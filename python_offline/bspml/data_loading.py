"""
Data Loading Module

This module provides functions to load PPG data, accelerometer data, and ground truth
from the PPG Dalia dataset stored in CSV format.
"""

import numpy as np
import pandas as pd
import os
from typing import Optional, Tuple, Dict, Any


def load_ppg_dalia_data(
    subject_id: str = "S1",
    duration: Optional[float] = None,
    start_time: float = 0,
    data_path: str = "data/ppg_dalia",
) -> Tuple[np.ndarray, float, Dict]:
    """
    Load PPG data from the PPG Dalia dataset.
    
    Args:
        subject_id: Subject identifier (S1, S2, S3, S4, S5)
        duration: Duration in seconds to load (None for full signal)
        start_time: Start time offset in seconds
        data_path: Path to the PPG Dalia dataset
        
    Returns:
        Tuple of (ppg_signal, sampling_rate, metadata)
    """
    subject_path = os.path.join(data_path, subject_id)
    
    if not os.path.exists(subject_path):
        raise FileNotFoundError(f"Subject path not found: {subject_path}")
    
    # Load BVP (PPG) data
    bvp_file = os.path.join(subject_path, "BVP.csv")
    if not os.path.exists(bvp_file):
        raise FileNotFoundError(f"BVP file not found: {bvp_file}")
    
    with open(bvp_file, 'r') as f:
        lines = f.read().strip().split('\n')
    
    # First line is start timestamp, second line is sampling frequency
    start_timestamp = float(lines[0])
    sampling_freq = float(lines[1])
    
    # Rest are PPG values
    ppg_values = np.array([float(line) for line in lines[2:]])
    
    # Initialize metadata
    metadata = {
        "start_timestamp": start_timestamp,
        "sampling_freq": sampling_freq,
        "data_source": "PPG_Dalia_BVP",
        "subject_id": subject_id
    }
    
    # Load additional metadata if available
    try:
        # Load activity data
        activity_file = os.path.join(subject_path, f"{subject_id}_activity.csv")
        if os.path.exists(activity_file):
            activity_df = pd.read_csv(activity_file)
            activities = []
            for _, row in activity_df.iterrows():
                if len(row) >= 2:
                    activity_name = str(row.iloc[0]).strip().replace("# ", "")
                    timestamp = row.iloc[1]
                    if activity_name != "SUBJECT_ID":
                        activities.append({"activity": activity_name, "timestamp": timestamp})
            metadata["activities"] = activities
    except Exception as e:
        print(f"Warning: Could not load activity data: {e}")
    
    try:
        # Load subject info
        quest_file = os.path.join(subject_path, f"{subject_id}_quest.csv")
        if os.path.exists(quest_file):
            quest_df = pd.read_csv(quest_file)
            subject_info = {}
            for _, row in quest_df.iterrows():
                if len(row) >= 2:
                    key = str(row.iloc[0]).strip().replace("# ", "")
                    value = row.iloc[1]
                    if key != "SUBJECT_ID":
                        subject_info[key.lower()] = value
            metadata["subject_info"] = subject_info
    except Exception as e:
        print(f"Warning: Could not load subject info: {e}")
    
    # Apply time windowing
    if start_time > 0 or duration is not None:
        start_sample = int(start_time * sampling_freq)
        
        if duration is not None:
            end_sample = start_sample + int(duration * sampling_freq)
            end_sample = min(end_sample, len(ppg_values))
        else:
            end_sample = len(ppg_values)
        
        ppg_values = ppg_values[start_sample:end_sample]
    
    ppg_signal = ppg_values.astype(np.float64)
    
    # Update metadata with final signal properties
    metadata["duration"] = len(ppg_signal) / sampling_freq
    metadata["num_samples"] = len(ppg_signal)
    metadata["start_time"] = start_time
    metadata["requested_duration"] = duration
    
    return ppg_signal, sampling_freq, metadata


def load_accelerometer_data(
    subject_id: str = "S1",
    duration: Optional[float] = None,
    start_time: float = 0,
    data_path: str = "data/ppg_dalia",
) -> Tuple[np.ndarray, float, Dict]:
    """
    Load accelerometer data from the PPG Dalia dataset.
    
    Args:
        subject_id: Subject identifier (S1, S2, S3, S4, S5)
        duration: Duration in seconds to load (None for full signal)
        start_time: Start time offset in seconds
        data_path: Path to the PPG Dalia dataset
        
    Returns:
        Tuple of (acc_signals, sampling_rate, metadata)
    """
    subject_path = os.path.join(data_path, subject_id)
    
    if not os.path.exists(subject_path):
        raise FileNotFoundError(f"Subject path not found: {subject_path}")
    
    # Load ACC data
    acc_file = os.path.join(subject_path, "ACC.csv")
    if not os.path.exists(acc_file):
        raise FileNotFoundError(f"ACC file not found: {acc_file}")
    
    with open(acc_file, 'r') as f:
        lines = f.read().strip().split('\n')
    
    # First line contains start timestamps for each axis (should be same)
    start_timestamps = [float(x.strip()) for x in lines[0].split(',')]
    start_timestamp = start_timestamps[0]  # Use first timestamp
    
    # Second line contains sampling frequencies for each axis (should be same)
    sampling_freqs = [float(x.strip()) for x in lines[1].split(',')]
    sampling_freq = sampling_freqs[0]  # Use first sampling frequency
    
    # Rest are accelerometer values (x, y, z)
    acc_data = []
    for line in lines[2:]:
        values = [float(x.strip()) for x in line.split(',')]
        if len(values) == 3:  # Ensure we have x, y, z values
            acc_data.append(values)
    
    acc_signals = np.array(acc_data)  # Shape: [n_samples, 3]
    
    # Initialize metadata
    metadata = {
        "start_timestamp": start_timestamp,
        "sampling_freq": sampling_freq,
        "data_source": "PPG_Dalia_ACC",
        "subject_id": subject_id,
        "axes": ["x", "y", "z"]
    }
    
    # Apply time windowing
    if start_time > 0 or duration is not None:
        start_sample = int(start_time * sampling_freq)
        
        if duration is not None:
            end_sample = start_sample + int(duration * sampling_freq)
            end_sample = min(end_sample, len(acc_signals))
        else:
            end_sample = len(acc_signals)
        
        acc_signals = acc_signals[start_sample:end_sample]
    
    # Update metadata with final signal properties
    metadata["duration"] = len(acc_signals) / sampling_freq
    metadata["num_samples"] = len(acc_signals)
    metadata["start_time"] = start_time
    metadata["requested_duration"] = duration
    
    return acc_signals, sampling_freq, metadata


def load_ground_truth_data(
    subject_id: str = "S1",
    start_time: float = 0,
    duration: Optional[float] = None,
    data_path: str = "data/ppg_dalia",
) -> Dict:
    """
    Load ground truth heart rate data from PPG Dalia dataset.
    
    Args:
        subject_id: Subject identifier (S1, S2, S3, S4, S5)
        start_time: Start time offset in seconds
        duration: Duration in seconds to load (None for full signal)
        data_path: Path to the PPG Dalia dataset
        
    Returns:
        Dictionary containing ground truth data
    """
    subject_path = os.path.join(data_path, subject_id)
    ground_truth = {}
    
    # Load ground truth heart rate from HR.csv
    hr_file = os.path.join(subject_path, "HR.csv")
    
    try:
        if os.path.exists(hr_file):
            with open(hr_file, 'r') as f:
                lines = f.read().strip().split('\n')
            
            # First line is start timestamp, second line is sampling frequency
            hr_start_timestamp = float(lines[0])
            hr_sampling_freq = float(lines[1])
            
            # Rest are heart rate values in bpm
            hr_values = np.array([float(line) for line in lines[2:] if line.strip()])
            
            # Create time axis for HR data
            hr_time_axis = np.arange(len(hr_values)) / hr_sampling_freq
            
            # Apply time windowing
            if start_time > 0 or duration is not None:
                start_idx = int(start_time * hr_sampling_freq)
                
                if duration is not None:
                    end_idx = start_idx + int(duration * hr_sampling_freq)
                    end_idx = min(end_idx, len(hr_values))
                else:
                    end_idx = len(hr_values)
                
                hr_values = hr_values[start_idx:end_idx]
                hr_time_axis = hr_time_axis[start_idx:end_idx]
            
            ground_truth["heart_rate"] = {
                "values": hr_values,
                "time_axis": hr_time_axis,
                "sampling_freq": hr_sampling_freq,
                "mean": np.mean(hr_values),
                "std": np.std(hr_values),
                "min": np.min(hr_values),
                "max": np.max(hr_values),
                "start_timestamp": hr_start_timestamp,
            }
        else:
            print(f"Warning: HR file not found: {hr_file}")
            ground_truth["heart_rate"] = None
            
    except Exception as e:
        print(f"Could not load ground truth heart rate: {e}")
        ground_truth["heart_rate"] = None
    
    return ground_truth


def get_available_subjects(data_path: str = "data/ppg_dalia") -> list:
    """
    Get list of available subjects in the dataset.
    
    Args:
        data_path: Path to the PPG Dalia dataset
        
    Returns:
        List of available subject IDs
    """
    if not os.path.exists(data_path):
        return []
    
    subjects = []
    for item in os.listdir(data_path):
        item_path = os.path.join(data_path, item)
        if os.path.isdir(item_path) and item.startswith('S'):
            subjects.append(item)
    
    return sorted(subjects)
