"""
Preprocessing Pipeline Module

Combines all preprocessing steps into a unified pipeline for PPG signal processing.
"""

import numpy as np
from typing import Optional, Dict, Any

from .detrending import wavelet_detrend
from .denoising import bandpass_filter
from .motion_artifacts import rls_filter


def preprocess_ppg(
    ppg_signal: np.ndarray,
    acc_signals: Optional[np.ndarray] = None,
    sampling_rate: float = 64.0,
    enable_detrending: bool = True,
    enable_denoising: bool = True,
    enable_motion_removal: bool = True,
    detrending_method: str = 'wavelet',
    denoising_method: str = 'bandpass',
    motion_removal_method: str = 'rls',
    preprocessing_params: Optional[Dict[str, Any]] = None
) -> np.ndarray:
    """
    Complete PPG signal preprocessing pipeline.

    This function applies the complete preprocessing pipeline including:
    1. Detrending (baseline drift removal)
    2. Denoising (high-frequency noise removal)
    3. Motion artifact removal (if accelerometer data available)

    Args:
        ppg_signal: Input PPG signal array
        acc_signals: Accelerometer signals [n_samples, n_axes] (optional)
        sampling_rate: Sampling rate in Hz
        enable_detrending: Whether to apply detrending
        enable_denoising: Whether to apply denoising
        enable_motion_removal: Whether to apply motion artifact removal
        detrending_method: Detrending method ('wavelet')
        denoising_method: Denoising method ('bandpass')
        motion_removal_method: Motion removal method ('rls')
        preprocessing_params: Additional parameters for preprocessing methods

    Returns:
        Preprocessed PPG signal


    """
    if len(ppg_signal) == 0:
        raise ValueError("PPG signal cannot be empty")

    if not np.isfinite(ppg_signal).all():
        raise ValueError("PPG signal contains non-finite values")

    # Initialize parameters
    if preprocessing_params is None:
        preprocessing_params = {}

    # Start with original signal
    processed_signal = ppg_signal.copy()

    if enable_detrending:

        if detrending_method == 'wavelet':
            wavelet_params = preprocessing_params.get('wavelet', {})
            processed_signal = wavelet_detrend(
                processed_signal, **wavelet_params)
        else:
            raise ValueError(f"Unknown detrending method: {detrending_method}")

    if enable_denoising:

        if denoising_method == 'bandpass':
            bandpass_params = preprocessing_params.get('bandpass', {})
            processed_signal = bandpass_filter(
                processed_signal,
                sampling_rate,
                **bandpass_params
            )
        else:
            raise ValueError(f"Unknown denoising method: {denoising_method}")

    if enable_motion_removal and acc_signals is not None:

        if motion_removal_method == 'rls':
            rls_params = preprocessing_params.get('rls', {})
            processed_signal = rls_filter(
                processed_signal,
                acc_signals,
                **rls_params
            )
        else:
            raise ValueError(
                f"Unknown motion removal method: {motion_removal_method}")

    return processed_signal


def get_default_preprocessing_params() -> Dict[str, Any]:
    """
    Get default preprocessing parameters.

    Returns:
        Dictionary with default parameters for all preprocessing methods
    """
    return {
        'wavelet': {
            'wavelet': 'db4',
            'levels': None,
            'mode': 'symmetric'
        },
        'bandpass': {
            'low_cutoff': 0.5,
            'high_cutoff': 4.0,
            'filter_order': 4,
            'filter_type': 'butterworth'
        },
        'rls': {
            'filter_order': 8,
            'forgetting_factor': 0.99
        }
    }
