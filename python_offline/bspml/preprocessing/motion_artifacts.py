"""
Motion Artifact Removal Module

Implements RLS (Recursive Least Squares) adaptive filtering using accelerometer
reference signals to remove motion-induced distortions from PPG signals.
"""

import numpy as np


class RLSFilter:
    """RLS adaptive filter for motion artifact removal using accelerometer reference."""

    def __init__(
        self,
        filter_order: int = 8,
        forgetting_factor: float = 0.99,
        regularization: float = 1e-6
    ):
        """
        Initialize RLS filter.

        Args:
            filter_order: Order of the adaptive filter
            forgetting_factor: Forgetting factor (0 < lambda <= 1)
            regularization: Regularization parameter for numerical stability
        """
        self.filter_order = filter_order
        self.forgetting_factor = forgetting_factor
        self.regularization = regularization

        # Initialize filter coefficients and covariance matrix
        self.weights = np.zeros(filter_order)
        self.P = np.eye(filter_order) / regularization

    def reset(self):
        """Reset filter state."""
        self.weights = np.zeros(self.filter_order)
        self.P = np.eye(self.filter_order) / self.regularization

    def update(self, reference_vector: np.ndarray, desired_signal: float) -> float:
        """
        Update filter with one sample.

        Args:
            reference_vector: Reference signal vector (accelerometer data)
            desired_signal: Desired signal sample (PPG)

        Returns:
            Filtered output sample
        """
        if len(reference_vector) != self.filter_order:
            raise ValueError(
                f"Reference vector must have length {self.filter_order}")

        # Calculate filter output
        output = np.dot(self.weights, reference_vector)

        # Calculate error
        error = desired_signal - output

        # Update covariance matrix
        k = self.P @ reference_vector
        alpha = 1 / (self.forgetting_factor + reference_vector.T @ k)
        self.P = (self.P - alpha * np.outer(k, k)) / self.forgetting_factor

        # Update weights
        self.weights += alpha * error * k

        return error  # Return cleaned signal (error signal)


def rls_filter(
    ppg_signal: np.ndarray,
    acc_signals: np.ndarray,
    filter_order: int = 8,
    forgetting_factor: float = 0.99
) -> np.ndarray:
    """
    Apply RLS adaptive filtering for motion artifact removal using accelerometer reference.

    Args:
        ppg_signal: Input PPG signal
        acc_signals: Accelerometer signals (N x 3 for x, y, z axes)
        filter_order: Adaptive filter order
        forgetting_factor: RLS forgetting factor

    Returns:
        Motion artifact corrected PPG signal
    """
    if len(ppg_signal) == 0:
        raise ValueError("PPG signal cannot be empty")

    if acc_signals.ndim != 2:
        raise ValueError("Accelerometer signals must be 2D array")

    if len(ppg_signal) != acc_signals.shape[0]:
        raise ValueError("PPG and accelerometer signals must have same length")

    n_samples, n_axes = acc_signals.shape

    # Create reference signal matrix by combining accelerometer axes
    # and their delayed versions to capture motion dynamics
    reference_matrix = create_reference_matrix(acc_signals, filter_order)

    # Initialize RLS filter
    rls = RLSFilter(filter_order, forgetting_factor)

    # Process signal sample by sample
    cleaned_signal = np.zeros_like(ppg_signal)

    for i in range(filter_order, n_samples):
        ref_vector = reference_matrix[i, :]
        cleaned_signal[i] = rls.update(ref_vector, ppg_signal[i])

    # Copy initial samples (before filter has enough history)
    cleaned_signal[:filter_order] = ppg_signal[:filter_order]

    return cleaned_signal


def create_reference_matrix(
    acc_signals: np.ndarray,
    filter_order: int
) -> np.ndarray:
    """
    Create reference signal matrix from accelerometer data.

    This function creates a matrix of reference signals by combining
    accelerometer axes and their time-delayed versions.

    Args:
        acc_signals: Accelerometer signals [n_samples, n_axes]
        filter_order: Filter order (number of taps)

    Returns:
        Reference matrix [n_samples, filter_order]
    """
    n_samples, n_axes = acc_signals.shape

    # Create time-delayed versions of accelerometer signals
    reference_matrix = np.zeros((n_samples, filter_order))

    # Fill reference matrix with delayed versions of accelerometer signals
    taps_per_axis = filter_order // n_axes
    remaining_taps = filter_order % n_axes

    col_idx = 0
    for axis in range(n_axes):
        # Number of taps for this axis
        n_taps = taps_per_axis + (1 if axis < remaining_taps else 0)

        for delay in range(n_taps):
            if col_idx < filter_order:
                if delay < n_samples:
                    reference_matrix[delay:, col_idx] = acc_signals[:-
                                                                    delay if delay > 0 else None, axis]
                col_idx += 1

    return reference_matrix
