#!/usr/bin/env python3
"""
Debug peak detection issues.
"""

import numpy as np
import matplotlib.pyplot as plt
from bspml import load_ppg_dalia_data, load_accelerometer_data, preprocess_ppg
from bspml.hr_estimation import estimate_heart_rate, detect_ppg_peaks
from bspml.hr_estimation.peak_detection import find_peaks_sliding_window


def debug_peak_detection():
    """Debug peak detection step by step."""
    
    print("=== DEBUGGING PEAK DETECTION ===")
    
    # Load and preprocess data
    ppg_signal, ppg_fs, _ = load_ppg_dalia_data(
        subject_id="S1", duration=60.0, start_time=60.0, data_path="../data/ppg_dalia"
    )
    acc_signals, acc_fs, _ = load_accelerometer_data(
        subject_id="S1", duration=60.0, start_time=60.0, data_path="../data/ppg_dalia"
    )
    
    print(f"Loaded PPG: {len(ppg_signal)} samples at {ppg_fs} Hz")
    
    # Preprocess (without RLS)
    ppg_processed = preprocess_ppg(
        ppg_signal=ppg_signal,
        acc_signals=acc_signals,
        sampling_rate=ppg_fs,
        acc_sampling_rate=acc_fs,
        enable_detrending=True,
        enable_denoising=True,
        enable_motion_removal=False
    )
    
    print(f"Processed PPG range: [{ppg_processed.min():.2f}, {ppg_processed.max():.2f}]")
    print(f"Processed PPG std: {ppg_processed.std():.2f}")
    
    # Test peak detection with different parameters
    print("\n1. Testing peak detection parameters...")
    
    test_params = [
        {'window_size': 10.0, 'min_peak_distance': 0.3, 'adaptive_threshold': True},  # Default
        {'window_size': 5.0, 'min_peak_distance': 0.3, 'adaptive_threshold': True},   # Smaller window
        {'window_size': 10.0, 'min_peak_distance': 0.2, 'adaptive_threshold': True},  # Closer peaks
        {'window_size': 10.0, 'min_peak_distance': 0.3, 'adaptive_threshold': False}, # Fixed threshold
        {'window_size': 15.0, 'min_peak_distance': 0.4, 'adaptive_threshold': True},  # Larger window
    ]
    
    results = {}
    
    for i, params in enumerate(test_params):
        try:
            peaks, peak_values = find_peaks_sliding_window(
                ppg_processed, ppg_fs, **params
            )
            results[f"Config_{i+1}"] = {'peaks': peaks, 'values': peak_values, 'params': params}
            print(f"  Config {i+1}: {len(peaks)} peaks found - {params}")
            
            if len(peaks) > 0:
                # Calculate average heart rate
                if len(peaks) > 1:
                    intervals = np.diff(peaks) / ppg_fs  # seconds
                    avg_hr = 60.0 / np.mean(intervals)
                    print(f"    Average HR: {avg_hr:.1f} BPM")
                
        except Exception as e:
            print(f"  Config {i+1}: ERROR - {e}")
    
    # Test on raw signal for comparison
    print("\n2. Testing on raw signal...")
    try:
        peaks_raw, values_raw = find_peaks_sliding_window(
            ppg_signal, ppg_fs, window_size=10.0, min_peak_distance=0.3, adaptive_threshold=True
        )
        print(f"  Raw signal: {len(peaks_raw)} peaks found")
        results["Raw"] = {'peaks': peaks_raw, 'values': values_raw}
    except Exception as e:
        print(f"  Raw signal: ERROR - {e}")
    
    # Test full HR estimation pipeline
    print("\n3. Testing full HR estimation...")
    try:
        hr_result = estimate_heart_rate(ppg_processed, ppg_fs, return_peaks=True)
        print(f"  HR estimation success: {hr_result.get('success', False)}")
        print(f"  Error: {hr_result.get('error', 'None')}")
        if 'peaks' in hr_result and isinstance(hr_result['peaks'], dict):
            num_peaks = len(hr_result['peaks'].get('indices', []))
            print(f"  Peaks from HR estimation: {num_peaks}")
    except Exception as e:
        print(f"  HR estimation: ERROR - {e}")
    
    # Create visualization
    print("\n4. Creating visualization...")
    
    fig, axes = plt.subplots(3, 1, figsize=(15, 10))
    fig.suptitle('Peak Detection Debug', fontsize=16)
    
    time_axis = np.arange(len(ppg_signal)) / ppg_fs
    
    # Plot raw signal with peaks
    axes[0].plot(time_axis, ppg_signal, 'b-', alpha=0.7, label='Raw PPG')
    if "Raw" in results and len(results["Raw"]['peaks']) > 0:
        peak_times = results["Raw"]['peaks'] / ppg_fs
        peak_values = ppg_signal[results["Raw"]['peaks']]
        axes[0].plot(peak_times, peak_values, 'ro', markersize=4, label=f'Peaks ({len(results["Raw"]["peaks"])})')
    axes[0].set_title('Raw PPG with Peak Detection')
    axes[0].set_ylabel('Amplitude')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)
    
    # Plot processed signal with peaks
    axes[1].plot(time_axis, ppg_processed, 'g-', alpha=0.7, label='Processed PPG')
    
    # Find best result (most peaks)
    best_config = None
    max_peaks = 0
    for config_name, result in results.items():
        if config_name != "Raw" and len(result['peaks']) > max_peaks:
            max_peaks = len(result['peaks'])
            best_config = config_name
    
    if best_config and max_peaks > 0:
        peak_indices = results[best_config]['peaks']
        peak_times = peak_indices / ppg_fs
        peak_values = ppg_processed[peak_indices]
        axes[1].plot(peak_times, peak_values, 'ro', markersize=4, 
                    label=f'Peaks ({len(peak_indices)}) - {best_config}')
    
    axes[1].set_title('Processed PPG with Peak Detection')
    axes[1].set_ylabel('Amplitude')
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)
    
    # Plot comparison
    axes[2].plot(time_axis, ppg_signal, 'b-', alpha=0.5, label='Raw PPG')
    axes[2].plot(time_axis, ppg_processed, 'g-', alpha=0.8, label='Processed PPG')
    axes[2].set_title('Raw vs Processed PPG')
    axes[2].set_ylabel('Amplitude')
    axes[2].set_xlabel('Time (seconds)')
    axes[2].legend()
    axes[2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # Save plot
    import os
    from datetime import datetime
    debug_dir = "debug_results"
    os.makedirs(debug_dir, exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    plot_path = os.path.join(debug_dir, f'peak_detection_debug_{timestamp}.png')
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"Peak detection debug plot saved to: {plot_path}")
    
    return results


def analyze_signal_characteristics():
    """Analyze signal characteristics that might affect peak detection."""
    
    print("\n=== ANALYZING SIGNAL CHARACTERISTICS ===")
    
    # Load and preprocess
    ppg_signal, ppg_fs, _ = load_ppg_dalia_data(
        subject_id="S1", duration=60.0, start_time=60.0, data_path="../data/ppg_dalia"
    )
    acc_signals, acc_fs, _ = load_accelerometer_data(
        subject_id="S1", duration=60.0, start_time=60.0, data_path="../data/ppg_dalia"
    )
    
    ppg_processed = preprocess_ppg(
        ppg_signal=ppg_signal,
        acc_signals=acc_signals,
        sampling_rate=ppg_fs,
        acc_sampling_rate=acc_fs,
        enable_detrending=True,
        enable_denoising=True,
        enable_motion_removal=False
    )
    
    # Analyze signal characteristics
    print(f"Raw signal:")
    print(f"  Mean: {ppg_signal.mean():.2f}")
    print(f"  Std: {ppg_signal.std():.2f}")
    print(f"  Min: {ppg_signal.min():.2f}")
    print(f"  Max: {ppg_signal.max():.2f}")
    print(f"  Range: {ppg_signal.max() - ppg_signal.min():.2f}")
    
    print(f"\nProcessed signal:")
    print(f"  Mean: {ppg_processed.mean():.2f}")
    print(f"  Std: {ppg_processed.std():.2f}")
    print(f"  Min: {ppg_processed.min():.2f}")
    print(f"  Max: {ppg_processed.max():.2f}")
    print(f"  Range: {ppg_processed.max() - ppg_processed.min():.2f}")
    
    # Check for potential issues
    if abs(ppg_processed.mean()) > ppg_processed.std():
        print("⚠️  Large DC offset in processed signal")
    
    if ppg_processed.std() < 1.0:
        print("⚠️  Very small signal amplitude - may affect peak detection")
    
    # Check signal polarity
    positive_peaks = np.sum(ppg_processed > ppg_processed.mean())
    negative_peaks = np.sum(ppg_processed < ppg_processed.mean())
    
    print(f"\nSignal polarity:")
    print(f"  Samples above mean: {positive_peaks}")
    print(f"  Samples below mean: {negative_peaks}")
    
    if positive_peaks < negative_peaks * 0.3:
        print("⚠️  Signal might be inverted - mostly negative values")


if __name__ == "__main__":
    results = debug_peak_detection()
    analyze_signal_characteristics()
