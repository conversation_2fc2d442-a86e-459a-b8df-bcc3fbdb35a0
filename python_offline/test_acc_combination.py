#!/usr/bin/env python3
"""
Test accelerometer channel combination functionality.
"""

import numpy as np
import matplotlib.pyplot as plt
from bspml import load_accelerometer_data
from bspml.preprocessing import combine_accelerometer_channels


def test_acc_combination():
    """Test accelerometer channel combination."""
    
    print("=== TESTING ACCELEROMETER CHANNEL COMBINATION ===")
    
    # Load real accelerometer data
    acc_signals, acc_fs, acc_meta = load_accelerometer_data(
        subject_id="S1", duration=60.0, start_time=60.0, data_path="../data/ppg_dalia"
    )
    
    print(f"Original ACC data shape: {acc_signals.shape}")
    print(f"Original ACC range: X[{acc_signals[:, 0].min():.1f}, {acc_signals[:, 0].max():.1f}], "
          f"Y[{acc_signals[:, 1].min():.1f}, {acc_signals[:, 1].max():.1f}], "
          f"Z[{acc_signals[:, 2].min():.1f}, {acc_signals[:, 2].max():.1f}]")
    
    # Test combination
    acc_combined = combine_accelerometer_channels(acc_signals)
    
    print(f"Combined ACC data shape: {acc_combined.shape}")
    print(f"Combined ACC range: [{acc_combined.min():.1f}, {acc_combined.max():.1f}]")
    print(f"Combined ACC std: {acc_combined.std():.2f}")
    
    # Calculate manual Euclidean norm for verification
    manual_norm = np.sqrt(np.sum(acc_signals**2, axis=1))
    
    # Check if they match
    if np.allclose(acc_combined.flatten(), manual_norm):
        print("✓ Combination matches manual Euclidean norm calculation")
    else:
        print("✗ Combination does NOT match manual calculation")
        print(f"Max difference: {np.max(np.abs(acc_combined.flatten() - manual_norm))}")
    
    # Test edge cases
    print("\nTesting edge cases:")
    
    # Test single channel input
    single_channel = acc_signals[:, 0:1]  # Keep as 2D
    combined_single = combine_accelerometer_channels(single_channel)
    print(f"Single channel input: {single_channel.shape} -> {combined_single.shape}")
    if np.allclose(single_channel, combined_single):
        print("✓ Single channel passthrough works")
    else:
        print("✗ Single channel passthrough failed")
    
    # Test 1D input
    acc_1d = acc_signals[:, 0]  # 1D array
    combined_1d = combine_accelerometer_channels(acc_1d)
    print(f"1D input: {acc_1d.shape} -> {combined_1d.shape}")
    if np.allclose(acc_1d.reshape(-1, 1), combined_1d):
        print("✓ 1D input handling works")
    else:
        print("✗ 1D input handling failed")
    
    # Create visualization
    print("\nCreating visualization...")
    
    fig, axes = plt.subplots(2, 1, figsize=(15, 8))
    fig.suptitle('Accelerometer Channel Combination', fontsize=16)
    
    time_axis = np.arange(len(acc_signals)) / acc_fs
    
    # Plot original channels
    axes[0].plot(time_axis, acc_signals[:, 0], 'r-', label='ACC X', alpha=0.7)
    axes[0].plot(time_axis, acc_signals[:, 1], 'g-', label='ACC Y', alpha=0.7)
    axes[0].plot(time_axis, acc_signals[:, 2], 'b-', label='ACC Z', alpha=0.7)
    axes[0].set_ylabel('Acceleration')
    axes[0].set_title('Original Accelerometer Channels')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)
    
    # Plot combined channel
    axes[1].plot(time_axis, acc_combined.flatten(), 'k-', linewidth=2, label='Combined (Euclidean Norm)')
    axes[1].set_ylabel('Acceleration Magnitude')
    axes[1].set_xlabel('Time (seconds)')
    axes[1].set_title('Combined Accelerometer Signal')
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # Save plot
    import os
    from datetime import datetime
    debug_dir = "debug_results"
    os.makedirs(debug_dir, exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    plot_path = os.path.join(debug_dir, f'acc_combination_{timestamp}.png')
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"Visualization saved to: {plot_path}")
    
    return acc_combined


def test_rls_with_combined_acc():
    """Test RLS filter with combined accelerometer data."""
    
    print("\n=== TESTING RLS WITH COMBINED ACCELEROMETER ===")
    
    # Load data
    from bspml import load_ppg_dalia_data, preprocess_ppg
    
    ppg_signal, ppg_fs, _ = load_ppg_dalia_data(
        subject_id="S1", duration=60.0, start_time=60.0, data_path="../data/ppg_dalia"
    )
    acc_signals, acc_fs, _ = load_accelerometer_data(
        subject_id="S1", duration=60.0, start_time=60.0, data_path="../data/ppg_dalia"
    )
    
    print(f"PPG: {len(ppg_signal)} samples")
    print(f"ACC: {acc_signals.shape}")
    
    # Test preprocessing with RLS enabled
    try:
        ppg_processed = preprocess_ppg(
            ppg_signal=ppg_signal,
            acc_signals=acc_signals,
            sampling_rate=ppg_fs,
            acc_sampling_rate=acc_fs,
            enable_detrending=True,
            enable_denoising=True,
            enable_motion_removal=True  # Enable RLS
        )
        
        print(f"✓ RLS preprocessing successful")
        print(f"Processed PPG range: [{ppg_processed.min():.2f}, {ppg_processed.max():.2f}]")
        print(f"Processed PPG std: {ppg_processed.std():.2f}")
        
        # Compare with RLS disabled
        ppg_no_rls = preprocess_ppg(
            ppg_signal=ppg_signal,
            acc_signals=acc_signals,
            sampling_rate=ppg_fs,
            acc_sampling_rate=acc_fs,
            enable_detrending=True,
            enable_denoising=True,
            enable_motion_removal=False  # Disable RLS
        )
        
        print(f"No RLS PPG std: {ppg_no_rls.std():.2f}")
        print(f"RLS effect on std: {ppg_processed.std() / ppg_no_rls.std():.3f}")
        
    except Exception as e:
        print(f"✗ RLS preprocessing failed: {e}")


if __name__ == "__main__":
    # Test accelerometer combination
    acc_combined = test_acc_combination()
    
    # Test RLS with combined accelerometer
    test_rls_with_combined_acc()
