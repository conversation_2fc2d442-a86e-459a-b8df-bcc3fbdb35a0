#!/usr/bin/env python3
"""
Flexible PPG Signal Processing Pipeline

This script can switch between PPG Dalia and real-world datasets with automatic
sampling rate detection and appropriate data loading strategies.
"""

import numpy as np
import matplotlib.pyplot as plt
import os
import json
import argparse
from datetime import datetime
from typing import Dict, Any, Optional, Tuple

# Import BSPML modules
from bspml import (
    load_ppg_dalia_data,
    load_accelerometer_data,
    load_ground_truth_data,
    load_realworld_ppg_data,
    load_realworld_accelerometer_data,
    load_realworld_ground_truth_data,
    detect_sampling_rate_from_timestamps,
    preprocess_ppg,
    estimate_heart_rate,
    evaluate_pipeline_performance,
    print_evaluation_summary
)


class DatasetConfig:
    """Configuration class for different dataset types."""
    
    def __init__(self, dataset_type: str):
        self.dataset_type = dataset_type
        
        if dataset_type == "ppg_dalia":
            self.ppg_sampling_rate = 64.0  # Default, will be auto-detected
            self.acc_sampling_rate = 32.0  # Default, will be auto-detected
            self.different_sampling_rates = True
        elif dataset_type == "realworld":
            self.ppg_sampling_rate = None  # Will be auto-detected
            self.acc_sampling_rate = None  # Will be auto-detected
            self.different_sampling_rates = False  # Usually same for real-world
        else:
            raise ValueError(f"Unknown dataset type: {dataset_type}")


def load_data_flexible(
    dataset_type: str,
    data_path: str,
    subject_id: Optional[str] = None,
    duration: Optional[float] = None,
    start_time: float = 0,
    ppg_file: Optional[str] = None,
    acc_file: Optional[str] = None,
    gt_file: Optional[str] = None,
    **kwargs
) -> Tuple[np.ndarray, np.ndarray, Dict, float, float, Dict]:
    """
    Flexible data loading that works with both PPG Dalia and real-world datasets.
    
    Returns:
        Tuple of (ppg_signal, acc_signals, ground_truth, ppg_fs, acc_fs, metadata)
    """
    
    if dataset_type == "ppg_dalia":
        # Load PPG Dalia data
        ppg_signal, ppg_fs, ppg_meta = load_ppg_dalia_data(
            subject_id=subject_id,
            duration=duration,
            start_time=start_time,
            data_path=data_path
        )
        
        acc_signals, acc_fs, acc_meta = load_accelerometer_data(
            subject_id=subject_id,
            duration=duration,
            start_time=start_time,
            data_path=data_path
        )
        
        ground_truth = load_ground_truth_data(
            subject_id=subject_id,
            start_time=start_time,
            duration=duration,
            data_path=data_path
        )
        
        metadata = {
            "dataset_type": "ppg_dalia",
            "subject_id": subject_id,
            "ppg_metadata": ppg_meta,
            "acc_metadata": acc_meta
        }
        
    elif dataset_type == "realworld":
        # Load real-world data
        if not ppg_file:
            raise ValueError("ppg_file must be specified for real-world data")
        
        ppg_signal, ppg_fs, ppg_meta = load_realworld_ppg_data(
            file_path=os.path.join(data_path, ppg_file),
            duration=duration,
            start_time=start_time,
            **kwargs
        )
        
        if acc_file:
            acc_signals, acc_fs, acc_meta = load_realworld_accelerometer_data(
                file_path=os.path.join(data_path, acc_file),
                duration=duration,
                start_time=start_time,
                **kwargs
            )
        else:
            # Create dummy accelerometer data if not available
            acc_signals = np.zeros((len(ppg_signal), 3))
            acc_fs = ppg_fs
            acc_meta = {"data_source": "dummy", "sampling_freq": ppg_fs}
        
        if gt_file:
            ground_truth = load_realworld_ground_truth_data(
                file_path=os.path.join(data_path, gt_file),
                start_time=start_time,
                duration=duration,
                **kwargs
            )
        else:
            ground_truth = {"heart_rate": None}
        
        metadata = {
            "dataset_type": "realworld",
            "ppg_file": ppg_file,
            "acc_file": acc_file,
            "gt_file": gt_file,
            "ppg_metadata": ppg_meta,
            "acc_metadata": acc_meta
        }
        
    else:
        raise ValueError(f"Unknown dataset type: {dataset_type}")
    
    return ppg_signal, acc_signals, ground_truth, ppg_fs, acc_fs, metadata


def validate_sampling_rates(ppg_fs: float, acc_fs: float, dataset_type: str) -> Tuple[float, float]:
    """
    Validate and potentially adjust sampling rates based on dataset type.
    """
    print(f"Detected sampling rates: PPG={ppg_fs:.1f}Hz, ACC={acc_fs:.1f}Hz")
    
    if dataset_type == "ppg_dalia":
        # PPG Dalia typically has different sampling rates
        if abs(ppg_fs - 64.0) > 5.0:
            print(f"Warning: PPG sampling rate {ppg_fs:.1f}Hz differs from expected 64Hz")
        if abs(acc_fs - 32.0) > 5.0:
            print(f"Warning: ACC sampling rate {acc_fs:.1f}Hz differs from expected 32Hz")
    
    elif dataset_type == "realworld":
        # Real-world data typically has same sampling rates
        if abs(ppg_fs - acc_fs) > 1.0:
            print(f"Warning: PPG and ACC have different sampling rates ({ppg_fs:.1f}Hz vs {acc_fs:.1f}Hz)")
            print("This is unusual for real-world data - please verify your data files")
    
    return ppg_fs, acc_fs


def create_output_directory(base_path: str = "results") -> str:
    """Create timestamped output directory for results."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = os.path.join(base_path, f"flexible_pipeline_{timestamp}")
    os.makedirs(output_dir, exist_ok=True)
    return output_dir


def save_results_to_json(results: Dict[str, Any], output_path: str):
    """Save results dictionary to JSON file."""
    # Convert numpy arrays to lists for JSON serialization
    json_results = {}
    for key, value in results.items():
        if isinstance(value, np.ndarray):
            json_results[key] = value.tolist()
        elif isinstance(value, dict):
            json_results[key] = {}
            for sub_key, sub_value in value.items():
                if isinstance(sub_value, np.ndarray):
                    json_results[key][sub_key] = sub_value.tolist()
                else:
                    json_results[key][sub_key] = sub_value
        else:
            json_results[key] = value
    
    with open(output_path, 'w') as f:
        json.dump(json_results, f, indent=2, default=str)


def plot_flexible_results(
    ppg_raw: np.ndarray,
    ppg_processed: np.ndarray,
    acc_signals: np.ndarray,
    hr_results: Dict[str, Any],
    ground_truth: Dict[str, Any],
    ppg_fs: float,
    acc_fs: float,
    output_dir: str,
    dataset_info: str
):
    """Create visualization for flexible pipeline results."""
    
    # Create figure with subplots
    fig, axes = plt.subplots(4, 1, figsize=(15, 12))
    fig.suptitle(f'Flexible PPG Processing Pipeline - {dataset_info}', fontsize=16)
    
    # Create time axis for PPG signals
    time_axis = np.arange(len(ppg_raw)) / ppg_fs
    
    # Plot 1: Raw vs Processed PPG
    axes[0].plot(time_axis, ppg_raw, 'b-', alpha=0.7, label='Raw PPG', linewidth=0.8)
    axes[0].plot(time_axis, ppg_processed, 'r-', label='Processed PPG', linewidth=1.0)
    axes[0].set_ylabel('PPG Amplitude')
    axes[0].set_title('Raw vs Processed PPG Signal')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)
    
    # Plot 2: Accelerometer signals (combined magnitude)
    from bspml.preprocessing import combine_accelerometer_channels, resample_accelerometer_data
    
    # Resample and combine accelerometer for visualization
    if acc_fs != ppg_fs:
        acc_resampled_viz = resample_accelerometer_data(acc_signals, acc_fs, ppg_fs, len(ppg_raw))
    else:
        acc_resampled_viz = acc_signals
    
    acc_combined_viz = combine_accelerometer_channels(acc_resampled_viz)
    acc_magnitude = acc_combined_viz.flatten()
    
    acc_time = np.arange(len(acc_magnitude)) / ppg_fs
    axes[1].plot(acc_time, acc_magnitude, 'purple', linewidth=2, label='ACC Magnitude (Combined)', alpha=0.8)
    axes[1].set_ylabel('Acceleration Magnitude')
    axes[1].set_title('Combined Accelerometer Signal')
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)
    
    # Plot 3: Detected peaks on processed signal
    if hr_results.get('success', False):
        # Show a portion of the signal with detected peaks
        window_start = len(ppg_processed) // 4
        window_end = min(window_start + int(30 * ppg_fs), len(ppg_processed))  # 30 seconds
        
        window_time = time_axis[window_start:window_end]
        window_signal = ppg_processed[window_start:window_end]
        
        axes[2].plot(window_time, window_signal, 'b-', linewidth=1.0)
        
        # Mark detected peaks if available
        if 'peaks' in hr_results and isinstance(hr_results['peaks'], dict):
            peak_indices = hr_results['peaks'].get('indices', np.array([]))
            if len(peak_indices) > 0:
                peak_mask = (peak_indices >= window_start) & (peak_indices < window_end)
                window_peaks = peak_indices[peak_mask] - window_start
                if len(window_peaks) > 0:
                    axes[2].plot(window_time[window_peaks], window_signal[window_peaks],
                                 'ro', markersize=6, label='Detected Peaks')
        
        axes[2].set_ylabel('PPG Amplitude')
        axes[2].set_title('Peak Detection (30s window)')
        axes[2].legend()
        axes[2].grid(True, alpha=0.3)
    else:
        axes[2].text(0.5, 0.5, 'Peak detection failed',
                    transform=axes[2].transAxes, ha='center', va='center')
        axes[2].set_title('Peak Detection - Failed')
    
    # Plot 4: Heart rate comparison
    if hr_results.get('success', False) and 'time_points' in hr_results:
        hr_time = hr_results['time_points']
        hr_values = hr_results['hr_values']
        axes[3].plot(hr_time, hr_values, 'r-', linewidth=2, label='Estimated HR')
        
        # Plot ground truth if available
        if ground_truth.get('heart_rate') is not None:
            gt_hr = ground_truth['heart_rate']
            gt_time = gt_hr['time_axis']
            gt_values = gt_hr['values']
            axes[3].plot(gt_time, gt_values, 'g-', linewidth=2, label='Ground Truth HR')
        
        axes[3].set_ylabel('Heart Rate (BPM)')
        axes[3].set_xlabel('Time (seconds)')
        axes[3].set_title('Heart Rate Estimation vs Ground Truth')
        axes[3].legend()
        axes[3].grid(True, alpha=0.3)
        axes[3].set_ylim(40, 120)  # Reasonable HR range
    else:
        axes[3].text(0.5, 0.5, 'Heart rate estimation failed',
                    transform=axes[3].transAxes, ha='center', va='center')
        axes[3].set_title('Heart Rate Estimation - Failed')
        axes[3].set_xlabel('Time (seconds)')
    
    plt.tight_layout()
    
    # Save plot
    plot_path = os.path.join(output_dir, f'flexible_pipeline_results.png')
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"Plot saved to: {plot_path}")


def run_flexible_pipeline(
    dataset_type: str,
    data_path: str,
    duration: Optional[float] = 60.0,
    start_time: float = 0,
    output_dir: Optional[str] = None,
    **kwargs
) -> Dict[str, Any]:
    """
    Run the flexible PPG processing pipeline.
    
    Args:
        dataset_type: "ppg_dalia" or "realworld"
        data_path: Path to dataset
        duration: Duration in seconds to process
        start_time: Start time offset in seconds
        output_dir: Output directory (auto-created if None)
        **kwargs: Additional arguments for data loading
        
    Returns:
        Dictionary containing all results
    """
    print(f"Starting flexible PPG processing pipeline")
    print(f"Dataset type: {dataset_type}")
    print(f"Duration: {duration}s, Start time: {start_time}s")
    print("-" * 50)
    
    # Create output directory
    if output_dir is None:
        output_dir = create_output_directory()
    
    results = {
        'dataset_type': dataset_type,
        'duration': duration,
        'start_time': start_time,
        'timestamp': datetime.now().isoformat()
    }
    
    try:
        # Load data
        print("1. Loading data...")
        ppg_signal, acc_signals, ground_truth, ppg_fs, acc_fs, metadata = load_data_flexible(
            dataset_type=dataset_type,
            data_path=data_path,
            duration=duration,
            start_time=start_time,
            **kwargs
        )
        
        print(f"   Loaded {len(ppg_signal)} PPG samples at {ppg_fs:.1f} Hz")
        print(f"   Loaded {len(acc_signals)} ACC samples at {acc_fs:.1f} Hz")
        
        # Validate sampling rates
        ppg_fs, acc_fs = validate_sampling_rates(ppg_fs, acc_fs, dataset_type)
        
        results['metadata'] = metadata
        results['ppg_sampling_rate'] = ppg_fs
        results['acc_sampling_rate'] = acc_fs
        
        # Check ground truth
        if ground_truth.get('heart_rate') is not None:
            gt_hr = ground_truth['heart_rate']
            print(f"   Ground truth HR: {gt_hr['mean']:.1f} ± {gt_hr['std']:.1f} BPM")
        else:
            print("   No ground truth HR data available")
        results['ground_truth'] = ground_truth
        
        # Preprocess PPG signal
        print("2. Preprocessing PPG signal...")
        print("   - Wavelet detrending")
        print("   - Bandpass filtering (0.5-4 Hz)")
        print("   - Adaptive RLS motion artifact removal")
        
        ppg_processed = preprocess_ppg(
            ppg_signal=ppg_signal,
            acc_signals=acc_signals,
            sampling_rate=ppg_fs,
            acc_sampling_rate=acc_fs,
            enable_detrending=True,
            enable_denoising=True,
            enable_motion_removal=True,
            adaptive_motion_removal=True,
            motion_threshold=100.0  # High threshold for conservative RLS usage
        )
        print(f"   Preprocessing completed")
        
        # Estimate heart rate
        print("3. Estimating heart rate...")
        hr_results = estimate_heart_rate(
            ppg_signal=ppg_processed,
            sampling_rate=ppg_fs,
            return_peaks=True
        )
        
        # Adjust time axis to account for start_time offset
        if hr_results.get('success', False) and 'time_points' in hr_results:
            hr_results['time_points'] = hr_results['time_points'] + start_time
            if 'peaks' in hr_results and isinstance(hr_results['peaks'], dict):
                if 'times' in hr_results['peaks']:
                    hr_results['peaks']['times'] = hr_results['peaks']['times'] + start_time
        
        if hr_results.get('success', False):
            hr_stats = hr_results.get('statistics', {})
            mean_hr = hr_stats.get('mean', None)
            std_hr = hr_stats.get('std', None)
            
            if mean_hr is not None and std_hr is not None:
                print(f"   Estimated HR: {mean_hr:.1f} ± {std_hr:.1f} BPM")
            else:
                print("   Estimated HR: Statistics not available")
            print(f"   Detected {hr_results.get('num_peaks', 0)} peaks")
        else:
            print(f"   Heart rate estimation failed: {hr_results.get('error', 'Unknown error')}")
        
        results['hr_estimation'] = hr_results
        
        # Evaluate performance
        print("4. Evaluating performance...")
        evaluation = evaluate_pipeline_performance(
            ppg_raw=ppg_signal,
            ppg_processed=ppg_processed,
            hr_results=hr_results,
            ground_truth=ground_truth,
            sampling_rate=ppg_fs
        )
        results['evaluation'] = evaluation
        
        # Print evaluation summary
        print_evaluation_summary(evaluation)
        
        # Create visualizations
        print("5. Creating visualizations...")
        dataset_info = f"{dataset_type.upper()}"
        if dataset_type == "ppg_dalia":
            dataset_info += f" - {kwargs.get('subject_id', 'Unknown')}"
        elif dataset_type == "realworld":
            dataset_info += f" - {kwargs.get('ppg_file', 'Unknown')}"
        
        plot_flexible_results(
            ppg_raw=ppg_signal,
            ppg_processed=ppg_processed,
            acc_signals=acc_signals,
            hr_results=hr_results,
            ground_truth=ground_truth,
            ppg_fs=ppg_fs,
            acc_fs=acc_fs,
            output_dir=output_dir,
            dataset_info=dataset_info
        )
        
        # Save results
        print("6. Saving results...")
        results_path = os.path.join(output_dir, f'flexible_results.json')
        save_results_to_json(results, results_path)
        print(f"   Results saved to: {results_path}")
        
        # Save evaluation separately for easy access
        eval_path = os.path.join(output_dir, f'evaluation.json')
        save_results_to_json(evaluation, eval_path)
        print(f"   Evaluation saved to: {eval_path}")
        
        print("-" * 50)
        print("Flexible pipeline completed successfully!")
        print(f"Output directory: {output_dir}")
        
        return results
        
    except Exception as e:
        print(f"Pipeline failed with error: {e}")
        results['error'] = str(e)
        results['success'] = False
        return results


def main():
    """Main function with command line argument parsing."""
    parser = argparse.ArgumentParser(description='Flexible PPG Processing Pipeline')
    
    # Dataset selection
    parser.add_argument('--dataset', choices=['ppg_dalia', 'realworld'], default='ppg_dalia',
                       help='Dataset type to process')
    parser.add_argument('--data_path', type=str, required=True,
                       help='Path to dataset directory')
    
    # Common parameters
    parser.add_argument('--duration', type=float, default=120.0,
                       help='Duration in seconds to process')
    parser.add_argument('--start_time', type=float, default=60.0,
                       help='Start time offset in seconds')
    parser.add_argument('--output_dir', type=str, default=None,
                       help='Output directory (auto-created if not specified)')
    
    # PPG Dalia specific
    parser.add_argument('--subject_id', type=str, default='S1',
                       help='Subject ID for PPG Dalia dataset')
    
    # Real-world specific
    parser.add_argument('--ppg_file', type=str, default=None,
                       help='PPG data file for real-world dataset')
    parser.add_argument('--acc_file', type=str, default=None,
                       help='Accelerometer data file for real-world dataset')
    parser.add_argument('--gt_file', type=str, default=None,
                       help='Ground truth file for real-world dataset')
    
    # Real-world CSV column names
    parser.add_argument('--timestamp_col', type=str, default='timestamp',
                       help='Timestamp column name')
    parser.add_argument('--ppg_col', type=str, default='ppg',
                       help='PPG column name')
    parser.add_argument('--acc_cols', type=str, nargs=3, default=['acc_x', 'acc_y', 'acc_z'],
                       help='Accelerometer column names (x y z)')
    parser.add_argument('--hr_col', type=str, default='heart_rate',
                       help='Heart rate column name')
    
    args = parser.parse_args()
    
    # Prepare kwargs for data loading
    kwargs = {}
    if args.dataset == 'ppg_dalia':
        kwargs['subject_id'] = args.subject_id
    elif args.dataset == 'realworld':
        kwargs['ppg_file'] = args.ppg_file
        kwargs['acc_file'] = args.acc_file
        kwargs['gt_file'] = args.gt_file
        kwargs['timestamp_column'] = args.timestamp_col
        kwargs['ppg_column'] = args.ppg_col
        kwargs['acc_columns'] = args.acc_cols
        kwargs['hr_column'] = args.hr_col
    
    # Run pipeline
    results = run_flexible_pipeline(
        dataset_type=args.dataset,
        data_path=args.data_path,
        duration=args.duration,
        start_time=args.start_time,
        output_dir=args.output_dir,
        **kwargs
    )
    
    return results


if __name__ == "__main__":
    results = main()
