#!/usr/bin/env python3
"""
Debug RLS filter implementation.
"""

import numpy as np
import matplotlib.pyplot as plt
from bspml import load_ppg_dalia_data, load_accelerometer_data
from bspml.preprocessing import bandpass_filter, resample_accelerometer_data
from bspml.preprocessing.motion_artifacts import rls_filter, create_reference_matrix, R<PERSON><PERSON>ilt<PERSON>


def debug_rls_filter():
    """Debug the RLS filter step by step."""
    
    print("=== DEBUGGING RLS FILTER ===")
    
    # Load a short segment
    ppg_signal, ppg_fs, _ = load_ppg_dalia_data(
        subject_id="S1", duration=30.0, start_time=60.0, data_path="../data/ppg_dalia"
    )
    acc_signals, acc_fs, _ = load_accelerometer_data(
        subject_id="S1", duration=30.0, start_time=60.0, data_path="../data/ppg_dalia"
    )
    
    # Apply bandpass filter first (as in pipeline)
    ppg_filtered = bandpass_filter(ppg_signal, ppg_fs)
    
    # Resample accelerometer
    acc_resampled = resample_accelerometer_data(acc_signals, acc_fs, ppg_fs, len(ppg_filtered))
    
    print(f"PPG signal: {len(ppg_filtered)} samples")
    print(f"ACC signal: {acc_resampled.shape}")
    print(f"PPG range: [{ppg_filtered.min():.2f}, {ppg_filtered.max():.2f}]")
    print(f"PPG std: {ppg_filtered.std():.2f}")
    print(f"ACC ranges: X[{acc_resampled[:, 0].min():.1f}, {acc_resampled[:, 0].max():.1f}]")
    print(f"ACC std: X={acc_resampled[:, 0].std():.1f}, Y={acc_resampled[:, 1].std():.1f}, Z={acc_resampled[:, 2].std():.1f}")
    
    # Test reference matrix creation
    print("\n1. Testing reference matrix creation...")
    filter_order = 8
    ref_matrix = create_reference_matrix(acc_resampled, filter_order)
    print(f"Reference matrix shape: {ref_matrix.shape}")
    print(f"Reference matrix range: [{ref_matrix.min():.2f}, {ref_matrix.max():.2f}]")
    print(f"Reference matrix std: {ref_matrix.std():.2f}")
    
    # Check for issues in reference matrix
    if np.any(np.isnan(ref_matrix)):
        print("WARNING: NaN values in reference matrix!")
    if np.any(np.isinf(ref_matrix)):
        print("WARNING: Infinite values in reference matrix!")
    
    # Test RLS filter with different parameters
    print("\n2. Testing RLS filter with different parameters...")
    
    test_params = [
        {'filter_order': 8, 'forgetting_factor': 0.99},  # Current
        {'filter_order': 4, 'forgetting_factor': 0.99},  # Smaller order
        {'filter_order': 8, 'forgetting_factor': 0.95},  # Faster adaptation
        {'filter_order': 8, 'forgetting_factor': 0.999}, # Slower adaptation
    ]
    
    results = {}
    
    for i, params in enumerate(test_params):
        try:
            rls_result = rls_filter(ppg_filtered, acc_resampled, **params)
            
            # Calculate statistics
            std_ratio = rls_result.std() / ppg_filtered.std()
            range_ratio = (rls_result.max() - rls_result.min()) / (ppg_filtered.max() - ppg_filtered.min())
            
            results[f"Config_{i+1}"] = rls_result
            
            print(f"  Config {i+1}: {params}")
            print(f"    Output range: [{rls_result.min():.2f}, {rls_result.max():.2f}]")
            print(f"    Output std: {rls_result.std():.2f} (ratio: {std_ratio:.3f})")
            print(f"    Range ratio: {range_ratio:.3f}")
            
            # Check for problematic values
            if np.any(np.isnan(rls_result)):
                print("    WARNING: NaN values in output!")
            if np.any(np.isinf(rls_result)):
                print("    WARNING: Infinite values in output!")
            if rls_result.std() > ppg_filtered.std() * 2:
                print("    WARNING: Output variance much larger than input!")
            
        except Exception as e:
            print(f"  Config {i+1}: ERROR - {e}")
    
    # Test without RLS (identity)
    print("\n3. Testing without RLS (identity)...")
    identity_result = ppg_filtered.copy()
    results["No_RLS"] = identity_result
    print(f"  Identity std: {identity_result.std():.2f}")
    
    # Create visualization
    print("\n4. Creating visualization...")
    
    fig, axes = plt.subplots(3, 2, figsize=(15, 12))
    fig.suptitle('RLS Filter Debug Analysis', fontsize=16)
    
    time_axis = np.arange(len(ppg_filtered)) / ppg_fs
    
    # Plot original signals
    axes[0, 0].plot(time_axis, ppg_signal, 'b-', label='Raw PPG', alpha=0.7)
    axes[0, 0].plot(time_axis, ppg_filtered, 'r-', label='Bandpass Filtered')
    axes[0, 0].set_title('PPG Signals')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # Plot accelerometer
    axes[0, 1].plot(time_axis, acc_resampled[:, 0], 'g-', label='ACC X', alpha=0.7)
    axes[0, 1].plot(time_axis, acc_resampled[:, 1], 'b-', label='ACC Y', alpha=0.7)
    axes[0, 1].plot(time_axis, acc_resampled[:, 2], 'r-', label='ACC Z', alpha=0.7)
    axes[0, 1].set_title('Accelerometer Signals')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # Plot RLS results
    plot_configs = list(results.keys())[:4]  # First 4 configs
    colors = ['blue', 'green', 'orange', 'red']
    
    for i, config_name in enumerate(plot_configs):
        row, col = (i // 2) + 1, i % 2
        if row < 3:  # Make sure we don't exceed subplot grid
            axes[row, col].plot(time_axis, ppg_filtered, 'k--', alpha=0.5, label='Input')
            axes[row, col].plot(time_axis, results[config_name], colors[i], label=f'{config_name}')
            axes[row, col].set_title(f'RLS Result: {config_name}')
            axes[row, col].legend()
            axes[row, col].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # Save plot
    import os
    from datetime import datetime
    debug_dir = "debug_results"
    os.makedirs(debug_dir, exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    plot_path = os.path.join(debug_dir, f'rls_debug_{timestamp}.png')
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"RLS debug plot saved to: {plot_path}")
    
    return results


def test_rls_theory():
    """Test RLS filter with synthetic data to verify implementation."""
    
    print("\n=== TESTING RLS WITH SYNTHETIC DATA ===")
    
    # Create synthetic test signals
    fs = 64.0
    duration = 10.0
    t = np.arange(0, duration, 1/fs)
    
    # Clean PPG-like signal (sum of sinusoids)
    clean_ppg = (np.sin(2 * np.pi * 1.2 * t) +  # ~72 BPM
                 0.5 * np.sin(2 * np.pi * 2.4 * t) +  # Harmonic
                 0.2 * np.random.randn(len(t)))  # Small noise
    
    # Motion artifact (correlated with accelerometer)
    motion_freq = 0.8  # Hz
    motion_artifact = 2.0 * np.sin(2 * np.pi * motion_freq * t + np.pi/4)
    
    # Synthetic accelerometer (source of motion)
    acc_x = np.sin(2 * np.pi * motion_freq * t)
    acc_y = 0.5 * np.cos(2 * np.pi * motion_freq * t)
    acc_z = 0.3 * np.sin(2 * np.pi * motion_freq * t + np.pi/2)
    acc_synthetic = np.column_stack([acc_x, acc_y, acc_z])
    
    # Corrupted PPG signal
    corrupted_ppg = clean_ppg + motion_artifact
    
    print(f"Synthetic signals created: {len(t)} samples")
    print(f"Clean PPG std: {clean_ppg.std():.3f}")
    print(f"Motion artifact std: {motion_artifact.std():.3f}")
    print(f"Corrupted PPG std: {corrupted_ppg.std():.3f}")
    
    # Apply RLS filter
    try:
        rls_cleaned = rls_filter(corrupted_ppg, acc_synthetic, filter_order=8, forgetting_factor=0.99)
        
        # Calculate performance metrics
        mse_before = np.mean((corrupted_ppg - clean_ppg) ** 2)
        mse_after = np.mean((rls_cleaned - clean_ppg) ** 2)
        
        print(f"MSE before RLS: {mse_before:.4f}")
        print(f"MSE after RLS: {mse_after:.4f}")
        print(f"MSE improvement: {mse_before / mse_after:.2f}x")
        
        if mse_after < mse_before:
            print("✓ RLS filter is working correctly on synthetic data")
        else:
            print("✗ RLS filter is not improving synthetic data")
            
    except Exception as e:
        print(f"ERROR in synthetic RLS test: {e}")


if __name__ == "__main__":
    # Debug RLS with real data
    results = debug_rls_filter()
    
    # Test with synthetic data
    test_rls_theory()
