#!/usr/bin/env python3
"""
Test delay compensation functionality.
"""

import numpy as np
import matplotlib.pyplot as plt
from bspml import load_ppg_dalia_data, load_accelerometer_data, preprocess_ppg
from bspml.preprocessing.motion_artifacts import detect_optimal_delay, apply_delay_compensation, rls_filter
from bspml.preprocessing import combine_accelerometer_channels, resample_accelerometer_data


def test_delay_compensation():
    """Test delay compensation functionality."""
    
    print("=== TESTING DELAY COMPENSATION ===")
    
    # Load data
    ppg_signal, ppg_fs, _ = load_ppg_dalia_data(
        subject_id="S1", duration=60.0, start_time=300.0, data_path="../data/ppg_dalia"
    )
    acc_signals, acc_fs, _ = load_accelerometer_data(
        subject_id="S1", duration=60.0, start_time=300.0, data_path="../data/ppg_dalia"
    )
    
    print(f"PPG: {len(ppg_signal)} samples at {ppg_fs} Hz")
    print(f"ACC: {acc_signals.shape} at {acc_fs} Hz")
    
    # Resample and combine accelerometer
    acc_resampled = resample_accelerometer_data(acc_signals, acc_fs, ppg_fs, len(ppg_signal))
    acc_combined = combine_accelerometer_channels(acc_resampled)
    acc_magnitude = acc_combined.flatten()
    
    print(f"ACC resampled: {len(acc_magnitude)} samples")
    
    # Test delay detection
    print("\n1. Testing delay detection...")
    optimal_delay = detect_optimal_delay(ppg_signal, acc_magnitude)
    print(f"Detected optimal delay: {optimal_delay} samples ({optimal_delay/ppg_fs:.3f}s)")
    
    # Test delay compensation
    print("\n2. Testing delay compensation...")
    acc_compensated = apply_delay_compensation(acc_combined, optimal_delay)
    
    print(f"Original ACC shape: {acc_combined.shape}")
    print(f"Compensated ACC shape: {acc_compensated.shape}")
    
    # Test RLS with different delay settings
    print("\n3. Testing RLS with delay compensation...")
    
    # Preprocess PPG first (without RLS)
    ppg_preprocessed = preprocess_ppg(
        ppg_signal=ppg_signal,
        acc_signals=acc_signals,
        sampling_rate=ppg_fs,
        acc_sampling_rate=acc_fs,
        enable_detrending=True,
        enable_denoising=True,
        enable_motion_removal=False
    )
    
    # Test RLS with different delay settings
    test_configs = [
        {"delay_compensation": None, "auto_delay_detection": False, "name": "No delay compensation"},
        {"delay_compensation": optimal_delay, "auto_delay_detection": False, "name": f"Manual delay ({optimal_delay} samples)"},
        {"delay_compensation": None, "auto_delay_detection": True, "name": "Auto delay detection"},
    ]
    
    results = {}
    
    for config in test_configs:
        try:
            print(f"\nTesting: {config['name']}")
            
            rls_result = rls_filter(
                ppg_preprocessed,
                acc_resampled,
                filter_order=8,
                forgetting_factor=0.99,
                delay_compensation=config.get("delay_compensation"),
                auto_delay_detection=config.get("auto_delay_detection", False)
            )
            
            results[config['name']] = rls_result
            
            # Calculate statistics
            std_ratio = rls_result.std() / ppg_preprocessed.std()
            print(f"  Output std ratio: {std_ratio:.3f}")
            print(f"  Output range: [{rls_result.min():.2f}, {rls_result.max():.2f}]")
            
        except Exception as e:
            print(f"  ERROR: {e}")
            results[config['name']] = None
    
    # Create visualization
    print("\n4. Creating visualization...")
    
    fig, axes = plt.subplots(3, 1, figsize=(15, 10))
    fig.suptitle('Delay Compensation Test', fontsize=16)
    
    time_axis = np.arange(len(ppg_signal)) / ppg_fs
    
    # Plot 1: Original signals
    axes[0].plot(time_axis, ppg_signal, 'b-', alpha=0.7, label='PPG')
    axes[0].plot(time_axis, acc_magnitude, 'r-', alpha=0.7, label='ACC Magnitude')
    axes[0].set_title('Original Signals')
    axes[0].set_ylabel('Amplitude')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)
    
    # Plot 2: Delay compensation effect
    axes[1].plot(time_axis, acc_magnitude, 'r-', alpha=0.7, label='Original ACC')
    axes[1].plot(time_axis, acc_compensated.flatten(), 'g-', alpha=0.7, 
                label=f'Compensated ACC (delay: {optimal_delay})')
    axes[1].set_title('Delay Compensation Effect')
    axes[1].set_ylabel('ACC Magnitude')
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)
    
    # Plot 3: RLS results comparison
    axes[2].plot(time_axis, ppg_preprocessed, 'k-', alpha=0.5, label='Preprocessed PPG (no RLS)')
    
    colors = ['blue', 'red', 'green']
    for i, (name, result) in enumerate(results.items()):
        if result is not None:
            axes[2].plot(time_axis, result, colors[i], alpha=0.8, label=name)
    
    axes[2].set_title('RLS Results Comparison')
    axes[2].set_ylabel('PPG Amplitude')
    axes[2].set_xlabel('Time (seconds)')
    axes[2].legend()
    axes[2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # Save plot
    import os
    from datetime import datetime
    debug_dir = "debug_results"
    os.makedirs(debug_dir, exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    plot_path = os.path.join(debug_dir, f'delay_compensation_test_{timestamp}.png')
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"Delay compensation test plot saved to: {plot_path}")
    
    return results


def test_synthetic_delay():
    """Test delay compensation with synthetic data where we know the true delay."""
    
    print("\n=== TESTING WITH SYNTHETIC DELAY ===")
    
    # Create synthetic signals
    fs = 64.0
    duration = 10.0
    t = np.arange(0, duration, 1/fs)
    
    # Create motion signal
    motion_freq = 0.8  # Hz
    motion_signal = np.sin(2 * np.pi * motion_freq * t)
    
    # Create PPG with motion artifact (delayed)
    true_delay = 5  # samples
    ppg_clean = np.sin(2 * np.pi * 1.2 * t)  # Heart rate signal
    motion_artifact = 0.5 * np.roll(motion_signal, true_delay)  # Delayed motion
    ppg_corrupted = ppg_clean + motion_artifact
    
    print(f"True delay: {true_delay} samples ({true_delay/fs:.3f}s)")
    
    # Test delay detection
    detected_delay = detect_optimal_delay(ppg_corrupted, motion_signal)
    print(f"Detected delay: {detected_delay} samples ({detected_delay/fs:.3f}s)")
    print(f"Detection error: {abs(detected_delay - true_delay)} samples")
    
    # Test delay compensation
    motion_compensated = apply_delay_compensation(motion_signal.reshape(-1, 1), detected_delay)
    
    # Simple correlation test
    corr_original = np.corrcoef(ppg_corrupted, motion_signal)[0, 1]
    corr_compensated = np.corrcoef(ppg_corrupted, motion_compensated.flatten())[0, 1]
    
    print(f"Correlation (original): {corr_original:.4f}")
    print(f"Correlation (compensated): {corr_compensated:.4f}")
    print(f"Improvement: {abs(corr_compensated) - abs(corr_original):.4f}")
    
    if abs(detected_delay - true_delay) <= 2:
        print("✓ Delay detection working correctly")
    else:
        print("✗ Delay detection has significant error")


if __name__ == "__main__":
    # Test with real data
    results = test_delay_compensation()
    
    # Test with synthetic data
    test_synthetic_delay()
