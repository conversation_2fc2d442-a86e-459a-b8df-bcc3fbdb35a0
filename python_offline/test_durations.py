#!/usr/bin/env python3
"""
Test different durations to identify where preprocessing issues start.
"""

import numpy as np
from bspml import (
    load_ppg_dalia_data,
    load_accelerometer_data,
    preprocess_ppg,
    evaluate_pipeline_performance,
    load_ground_truth_data
)
from bspml.hr_estimation import estimate_heart_rate


def test_duration(duration, start_time=60.0, subject_id="S1"):
    """Test pipeline with specific duration."""
    print(f"\n=== Testing duration: {duration}s ===")
    
    try:
        # Load data
        ppg_signal, ppg_fs, _ = load_ppg_dalia_data(
            subject_id=subject_id, duration=duration, start_time=start_time, data_path="../data/ppg_dalia"
        )
        acc_signals, acc_fs, _ = load_accelerometer_data(
            subject_id=subject_id, duration=duration, start_time=start_time, data_path="../data/ppg_dalia"
        )
        ground_truth = load_ground_truth_data(
            subject_id=subject_id, start_time=start_time, duration=duration, data_path="../data/ppg_dalia"
        )
        
        print(f"Loaded: PPG {len(ppg_signal)} samples, ACC {len(acc_signals)} samples")
        
        # Preprocess
        ppg_processed = preprocess_ppg(
            ppg_signal=ppg_signal,
            acc_signals=acc_signals,
            sampling_rate=ppg_fs,
            acc_sampling_rate=acc_fs,
            enable_detrending=True,
            enable_denoising=True,
            enable_motion_removal=True
        )
        
        print(f"Preprocessing completed")
        
        # Heart rate estimation
        hr_results = estimate_heart_rate(
            ppg_signal=ppg_processed,
            sampling_rate=ppg_fs,
            return_peaks=True
        )
        
        num_peaks = 0
        if hr_results.get('success') and 'peaks' in hr_results:
            num_peaks = len(hr_results['peaks'].get('indices', []))
        
        print(f"HR estimation: {hr_results.get('success', False)}, {num_peaks} peaks")
        
        # Evaluation
        evaluation = evaluate_pipeline_performance(
            ppg_raw=ppg_signal,
            ppg_processed=ppg_processed,
            hr_results=hr_results,
            ground_truth=ground_truth,
            sampling_rate=ppg_fs
        )
        
        delta_snr = evaluation.get('delta_snr_db', float('nan'))
        hr_error = evaluation.get('hr_error_percent', float('nan'))
        
        print(f"ΔSNR: {delta_snr:.2f} dB")
        print(f"HR error: {hr_error:.1f}%")
        
        return {
            'duration': duration,
            'success': True,
            'num_peaks': num_peaks,
            'delta_snr': delta_snr,
            'hr_error': hr_error,
            'ppg_std_raw': ppg_signal.std(),
            'ppg_std_processed': ppg_processed.std()
        }
        
    except Exception as e:
        print(f"ERROR: {e}")
        return {
            'duration': duration,
            'success': False,
            'error': str(e)
        }


def main():
    """Test multiple durations to find where issues start."""
    
    # Test different durations
    durations = [30, 60, 120, 300, 600, 1200]  # 30s to 20 minutes
    
    results = []
    for duration in durations:
        result = test_duration(duration)
        results.append(result)
        
        if not result['success']:
            print(f"Failed at duration {duration}s, stopping tests")
            break
    
    # Summary
    print("\n" + "="*60)
    print("DURATION TEST SUMMARY")
    print("="*60)
    print(f"{'Duration':<10} {'Peaks':<8} {'ΔSNR':<10} {'HR Error':<10} {'Status'}")
    print("-" * 60)
    
    for result in results:
        if result['success']:
            print(f"{result['duration']:<10} {result['num_peaks']:<8} "
                  f"{result['delta_snr']:<10.2f} {result['hr_error']:<10.1f} OK")
        else:
            print(f"{result['duration']:<10} {'N/A':<8} {'N/A':<10} {'N/A':<10} FAILED")


if __name__ == "__main__":
    main()
